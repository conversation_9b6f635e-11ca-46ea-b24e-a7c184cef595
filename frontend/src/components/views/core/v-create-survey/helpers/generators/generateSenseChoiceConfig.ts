import { senseChoiceConfigInterface, ConjointProfile, ConjointChoiceTask } from '../../interfaces';

export const generateSenseChoiceConfig = (
  type: string,
  attributes: { label: string; value: string }[],
  attributeLevels: { [key: string]: { label: string; value: string }[] },
  selectedProfiles: ConjointProfile[],
  choiceTasks: ConjointChoiceTask[],
  thankYouMessage: string,
) => {
  let payload: senseChoiceConfigInterface = {
    type: type,
    attributes: attributes,
    attributeLevels: attributeLevels,
    selectedProfiles: selectedProfiles,
    choiceTasks: choiceTasks,
    thankYouMessage: thankYouMessage || 'Thank you for your response!',
  };

  return payload;
};
