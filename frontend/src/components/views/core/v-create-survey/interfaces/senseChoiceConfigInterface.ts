export interface ConjointProfile {
  profileId: string;
  attributes: { [key: string]: string }; // attributeValue -> selected level value
  selected?: boolean;
}

export interface ConjointChoiceTask {
  taskId: string;
  taskNumber: number;
  alternatives: ConjointProfile[];
  includeNoneOption: boolean;
  selected?: boolean;
}

export interface senseChoiceConfigInterface {
  type: string; // 'lite' or 'full'
  attributes: { label: string; value: string }[];
  attributeLevels: { [key: string]: { label: string; value: string }[] }; // attributeValue -> levels array
  selectedProfiles: ConjointProfile[]; // User-curated profiles
  choiceTasks: ConjointChoiceTask[]; // User-curated tasks
  thankYouMessage: string;
}
