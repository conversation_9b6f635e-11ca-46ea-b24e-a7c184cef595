import { Component, Element, Event, EventEmitter, Listen, State, h } from '@stencil/core';
import { FrontendLogger } from '../../../../global/script/var';

@Component({
  tag: 'p-level-form',
  styleUrl: '../p-priority-item-form/p-priority-item-form.css',
  shadow: true,
})
export class PLevelForm {
  @Element() el: HTMLElement;

  @Event({
    eventName: 'addCustomSenseChoiceLevel',
    bubbles: true,
  })
  addCustomSenseChoiceLevelEventEmitter: EventEmitter;

  @Event({
    eventName: 'modalCloseEvent',
    bubbles: true,
  })
  modalCloseEventEmitter: EventEmitter;

  @State() levelValue: string = '';
  @State() formErrors: { [key: string]: string } = {};

  componentDidLoad() {
    // Focus on the level input when modal opens
    const levelInput = this.el.shadowRoot?.querySelector(
      'e-input[name="levelValue"]',
    ) as HTMLElement;
    if (levelInput) {
      setTimeout(() => levelInput.focus(), 100);
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value } = event.detail;

    if (name === 'levelValue') {
      this.levelValue = value;
      // Clear level error when user starts typing
      if (this.formErrors.level) {
        this.formErrors = { ...this.formErrors };
        delete this.formErrors.level;
      }
    }
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'saveLevel') {
      this.saveLevel();
    } else if (event.detail.action === 'cancelLevel') {
      this.closeModal();
    }
  }

  private validateForm(): boolean {
    const errors: { [key: string]: string } = {};

    if (!this.levelValue.trim()) {
      errors.level = 'Level value is required';
    }

    this.formErrors = errors;
    return Object.keys(errors).length === 0;
  }

  private saveLevel() {
    if (!this.validateForm()) {
      return;
    }

    const level = this.levelValue.trim();

    FrontendLogger.debug('Emitting addCustomSenseChoiceLevel with:', level);

    this.addCustomSenseChoiceLevelEventEmitter.emit({
      level: level,
    });
  }

  private closeModal() {
    this.modalCloseEventEmitter.emit();
  }

  render() {
    return (
      <div class="priority-item-form">
        <e-text>
          <strong>
            Level value <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="levelValue"
          placeholder="e.g. Low, Medium, High"
          value={this.levelValue}
        ></e-input>
        <l-spacer value={0.5}></l-spacer>
        <e-text variant="footnote">
          Keep the level value short and clear. Use descriptive terms that respondents will easily
          understand.
        </e-text>
        {this.formErrors.level && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.level}</e-text>
          </div>
        )}
        <l-spacer value={3}></l-spacer>
        <l-row justifyContent="space-between">
          <e-button variant="light" action="cancelLevel">
            Cancel
          </e-button>
          <e-button action="saveLevel" disabled={!this.levelValue.trim()}>
            Add
          </e-button>
        </l-row>
      </div>
    );
  }
}
