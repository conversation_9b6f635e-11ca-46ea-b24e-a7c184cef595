import OpenAI from 'openai';
import { Var } from '../../../global/var';
import { logger } from '../../../global/services';

/**
 * OpenAI Service for AI-powered features
 * 
 * This service provides AI capabilities for generating realistic product profiles
 * and other AI-powered features in the SenseChoice survey system.
 */
class OpenAIService {
  private client: OpenAI;

  constructor() {
    this.client = new OpenAI({
      apiKey: Var.ai.openai.apiKey,
    });
  }

  /**
   * Generate realistic product profiles using AI
   * 
   * @param attributes - Array of attributes with their levels
   * @param productContext - Context about the product/service being studied
   * @param maxProfiles - Maximum number of profiles to generate (default: 20)
   * @returns Promise<Array of realistic product profiles>
   */
  async generateRealisticProfiles(
    attributes: { label: string; value: string }[],
    attributeLevels: { [key: string]: { label: string; value: string }[] },
    productContext: {
      industry?: string;
      productType?: string;
      description?: string;
    } = {},
    maxProfiles: number = 20
  ): Promise<any[]> {
    try {
      const prompt = this.buildProfileGenerationPrompt(
        attributes,
        attributeLevels,
        productContext,
        maxProfiles
      );

      logger.debug('Generating AI profiles', {
        attributeCount: attributes.length,
        maxProfiles,
        productContext,
      });

      const response = await this.client.chat.completions.create({
        model: Var.ai.openai.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert in market research and product design. You generate realistic product profiles for conjoint analysis studies by combining attributes in ways that make business sense.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        response_format: { type: 'json_object' },
        temperature: 0.7,
        max_tokens: 4000,
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      
      if (!result.profiles || !Array.isArray(result.profiles)) {
        throw new Error('Invalid response format from AI service');
      }

      // Transform AI response to match frontend format
      const profiles = result.profiles.map((profile: any, index: number) => ({
        profileId: `ai_profile_${String(index + 1).padStart(3, '0')}`,
        attributes: profile.attributes,
        selected: false,
        aiGenerated: true,
        reasoning: profile.reasoning || '',
      }));

      logger.info('AI profiles generated successfully', {
        generatedCount: profiles.length,
        requestedMax: maxProfiles,
      });

      return profiles;

    } catch (error) {
      logger.error('Error generating AI profiles', {
        error: error instanceof Error ? error.message : String(error),
        attributes: attributes.map(a => a.label),
        productContext,
      });
      
      throw new Error('Failed to generate AI profiles. Please try again.');
    }
  }

  /**
   * Build the prompt for AI profile generation
   */
  private buildProfileGenerationPrompt(
    attributes: { label: string; value: string }[],
    attributeLevels: { [key: string]: { label: string; value: string }[] },
    productContext: any,
    maxProfiles: number
  ): string {
    const attributeInfo = attributes.map(attr => {
      const levels = attributeLevels[attr.value] || [];
      return `${attr.label}: [${levels.map(l => l.label).join(', ')}]`;
    }).join('\n');

    const contextInfo = productContext.description 
      ? `Product Context: ${productContext.description}`
      : `Product Type: ${productContext.productType || 'General Product'}, Industry: ${productContext.industry || 'General'}`;

    return `
Generate ${maxProfiles} realistic product profiles for a conjoint analysis study.

${contextInfo}

Available Attributes and Levels:
${attributeInfo}

Requirements:
1. Create combinations that make business sense and are realistic in the market
2. Avoid combinations that would be impossible or highly unlikely (e.g., premium brand at budget price)
3. Consider typical market positioning and customer expectations
4. Ensure good variety while maintaining realism
5. Include a brief reasoning for why each combination makes sense

Return the response as a JSON object with this exact structure:
{
  "profiles": [
    {
      "attributes": {
        "${attributes[0]?.value || 'attribute1'}": "level_value",
        "${attributes[1]?.value || 'attribute2'}": "level_value"
      },
      "reasoning": "Brief explanation of why this combination makes sense"
    }
  ]
}

Make sure all attribute values use the exact value strings provided in the levels, not the labels.
`;
  }

  /**
   * Validate if a profile combination makes sense using AI
   */
  async validateProfileCombination(
    profile: { [key: string]: string },
    attributes: { label: string; value: string }[],
    productContext: any = {}
  ): Promise<{ isValid: boolean; reasoning: string; confidence: number }> {
    try {
      const attributeLabels = attributes.reduce((acc, attr) => {
        acc[attr.value] = attr.label;
        return acc;
      }, {} as { [key: string]: string });

      const profileDescription = Object.entries(profile)
        .map(([attrValue, levelValue]) => `${attributeLabels[attrValue]}: ${levelValue}`)
        .join(', ');

      const prompt = `
Evaluate if this product profile combination makes business sense:

Product: ${profileDescription}
Context: ${productContext.description || `${productContext.productType || 'Product'} in ${productContext.industry || 'general'} industry`}

Respond with a JSON object:
{
  "isValid": true/false,
  "reasoning": "explanation of why this combination does or doesn't make sense",
  "confidence": 0.0-1.0 (confidence in the assessment)
}
`;

      const response = await this.client.chat.completions.create({
        model: Var.ai.openai.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert in market research and product positioning. Evaluate product combinations for realism and market viability.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        response_format: { type: 'json_object' },
        temperature: 0.3,
        max_tokens: 500,
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      
      return {
        isValid: result.isValid || false,
        reasoning: result.reasoning || 'No reasoning provided',
        confidence: result.confidence || 0.5,
      };

    } catch (error) {
      logger.error('Error validating profile combination', {
        error: error instanceof Error ? error.message : String(error),
        profile,
      });
      
      return {
        isValid: true, // Default to valid if AI fails
        reasoning: 'Unable to validate - assuming valid',
        confidence: 0.1,
      };
    }
  }
}

export const openAIService = new OpenAIService();
