import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { ApiLimiter } from '../../../security';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { generateSmartProfilesController } from '../../controllers/generateSmartProfilesController';

export const generateSmartProfilesRoute = Router();

/**
 * Smart Profile Generation Route - POST /v1/ai/generate-profiles
 * 
 * This endpoint uses AI to generate realistic product profiles for conjoint analysis.
 * Instead of creating all possible combinations (cartesian product), it intelligently
 * selects combinations that make business sense.
 * 
 * Features:
 * - AI-powered realistic combination generation
 * - Business logic validation
 * - Avoids nonsensical attribute combinations
 * - Supports product context for better results
 * 
 * Security:
 * - Requires authentication (logged-in user)
 * - Rate limited to prevent abuse
 * - Origin validation
 * 
 * Request Body:
 * {
 *   "attributes": [{"label": "Brand", "value": "brand"}],
 *   "attributeLevels": {"brand": [{"label": "Apple", "value": "apple"}]},
 *   "productContext": {"industry": "Technology", "productType": "Smartphone"},
 *   "maxProfiles": 20
 * }
 */
generateSmartProfilesRoute.post(
  `${GenerateApiVersionPath()}ai/generate-profiles`,
  // Rate limiting
  ApiLimiter,
  
  // Extract request information
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  
  // Authentication - require logged-in user
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  
  // Controller
  generateSmartProfilesController,
);
