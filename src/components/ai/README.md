# AI Integration for Conjoint Analysis Profile Generation

This module provides AI-powered conjoint analysis profile generation for SenseChoice surveys, replacing the traditional cartesian product approach with intelligent combination selection.

## Overview

Instead of generating all possible combinations of attributes and levels (which can create many unrealistic combinations), this AI service generates only realistic, market-viable product profiles.

## Features

- **Conjoint Profile Generation**: Uses OpenAI to generate realistic product combinations for conjoint analysis
- **Business Logic Validation**: Avoids nonsensical attribute combinations
- **Context-Aware**: Considers product type, industry, and description
- **Fallback Support**: Falls back to cartesian product if AI fails
- **Configurable**: Supports different AI models and parameters

## Setup

### 1. Environment Variables

Add these to your `.env` file:

```bash
# AI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4o-mini  # or gpt-4, gpt-3.5-turbo, etc.
```

### 2. Install Dependencies

```bash
npm install openai
```

## Usage

### Backend API

The AI service is exposed via the REST API:

```
POST /v1/ai/generate-conjoint-profiles
```

**Request Body:**

```json
{
  "attributes": [
    { "label": "Brand", "value": "brand" },
    { "label": "Storage", "value": "storage" },
    { "label": "Price", "value": "price" }
  ],
  "attributeLevels": {
    "brand": [
      { "label": "Apple", "value": "apple" },
      { "label": "Samsung", "value": "samsung" }
    ],
    "storage": [
      { "label": "64GB", "value": "64gb" },
      { "label": "128GB", "value": "128gb" }
    ],
    "price": [
      { "label": "$299", "value": "299" },
      { "label": "$599", "value": "599" }
    ]
  },
  "productContext": {
    "industry": "Technology",
    "productType": "Smartphone",
    "description": "Premium smartphone comparison study"
  },
  "maxProfiles": 20
}
```

**Response:**

```json
{
  "success": true,
  "message": "Generated 8 realistic conjoint analysis profiles",
  "payload": {
    "profiles": [
      {
        "profileId": "ai_profile_001",
        "attributes": {
          "brand": "apple",
          "storage": "128gb",
          "price": "599"
        },
        "selected": false,
        "aiGenerated": true,
        "reasoning": "Apple typically positions at premium price points with higher storage options"
      }
    ],
    "metadata": {
      "aiGenerated": true,
      "model": "gpt-4o-mini",
      "generatedAt": "2025-01-16T10:30:00Z",
      "inputAttributes": 3,
      "totalPossibleCombinations": 8
    }
  }
}
```

### Frontend Integration

The frontend automatically uses AI generation when creating SenseChoice surveys:

```typescript
// The AI generation is called automatically in the survey creation flow
await this.generateConjointProfilesWithAI();
```

## AI Models Supported

- **OpenAI GPT-4o-mini** (recommended for cost-effectiveness)
- **OpenAI GPT-4** (for maximum quality)
- **OpenAI GPT-3.5-turbo** (for basic use cases)

## Error Handling

The system includes comprehensive error handling:

1. **AI Service Unavailable**: Falls back to cartesian product
2. **Invalid API Key**: Returns configuration error
3. **Rate Limiting**: Implements retry logic
4. **Invalid Response**: Validates AI output format

## Benefits

### Before (Cartesian Product)

- **Brand**: [Apple, Samsung, Google] (3 options)
- **Storage**: [64GB, 128GB, 512GB, 1TB] (4 options)
- **Price**: [$299, $599, $899, $1299] (4 options)
- **Total**: 3 × 4 × 4 = **48 combinations**
- **Issues**: Many unrealistic (e.g., "Apple for $299", "Google with 1TB for $299")

### After (AI Generation)

- **Generated**: **12-20 realistic combinations**
- **Quality**: Only market-viable combinations
- **Examples**:
  - ✅ "Apple, 128GB, $599"
  - ✅ "Samsung, 64GB, $299"
  - ❌ Avoids "Apple, 64GB, $299"

## Configuration

### Customizing AI Behavior

You can customize the AI generation by modifying the prompt in `OpenAIService.ts`:

```typescript
// Adjust temperature for creativity vs consistency
temperature: 0.7, // 0.0 = deterministic, 1.0 = creative

// Adjust max profiles generated
maxProfiles: 20, // Default: 20

// Customize the prompt for your domain
const prompt = this.buildProfileGenerationPrompt(/* ... */);
```

### Alternative AI Providers

To use Claude or Gemini instead of OpenAI:

1. Create new service files (e.g., `ClaudeService.ts`)
2. Implement the same interface
3. Update the controller to use the new service

## Security

- API key is stored securely in environment variables
- Rate limiting prevents abuse
- Authentication required for API access
- Input validation on all parameters

## Monitoring

The service includes comprehensive logging:

```typescript
logger.info('AI profiles generated successfully', {
  generatedCount: profiles.length,
  accountId: res.locals?.accountId,
});
```

## Future Enhancements

- Support for multiple AI providers
- Custom business rules engine
- Profile quality scoring
- A/B testing between AI and cartesian methods
- Caching of generated profiles
