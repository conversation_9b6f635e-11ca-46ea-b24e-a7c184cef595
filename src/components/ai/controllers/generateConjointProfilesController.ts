import { Request, Response } from 'express';
import { Var } from '../../../global/var';
import { logger } from '../../../global/services';
import { openAIService } from '../services/OpenAIService';
import { validateAIConfiguration } from '../helpers/validateAIConfiguration';

/**
 * Controller to generate conjoint analysis profiles using AI
 *
 * This endpoint accepts survey attributes and levels, then uses AI to generate
 * realistic product combinations for conjoint analysis that make business sense,
 * avoiding the cartesian product problem of generating all possible combinations.
 *
 * @param req - Express request object
 * @param res - Express response object
 */
export const generateConjointProfilesController = async (req: Request, res: Response) => {
  try {
    const { attributes, attributeLevels, productContext, maxProfiles } = req.body;

    // Validate AI configuration
    const aiConfig = validateAIConfiguration();
    if (!aiConfig.isValid) {
      logger.warn('AI configuration invalid', {
        errors: aiConfig.errors,
        accountId: res.locals?.accountId,
      });

      return res.status(500).json({
        success: false,
        message: `${Var.app.emoji.failure} AI service is not properly configured`,
        errors: aiConfig.errors,
      });
    }

    // Validate required parameters
    if (!attributes || !Array.isArray(attributes) || attributes.length === 0) {
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Attributes are required`,
      });
    }

    if (!attributeLevels || typeof attributeLevels !== 'object') {
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Attribute levels are required`,
      });
    }

    // Validate that all attributes have levels
    const missingLevels = attributes.filter(attr => !attributeLevels[attr.value] || attributeLevels[attr.value].length === 0);

    if (missingLevels.length > 0) {
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Missing levels for attributes: ${missingLevels.map(a => a.label).join(', ')}`,
      });
    }

    logger.info('Generating conjoint analysis profiles with AI', {
      attributeCount: attributes.length,
      maxProfiles: maxProfiles || 20,
      accountId: res.locals?.accountId,
      productContext: productContext || {},
    });

    // Generate conjoint profiles using AI
    const profiles = await openAIService.generateRealisticConjointProfiles(attributes, attributeLevels, productContext || {}, maxProfiles || 20);

    logger.info('Conjoint analysis profiles generated successfully', {
      generatedCount: profiles.length,
      accountId: res.locals?.accountId,
    });

    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Generated ${profiles.length} realistic conjoint analysis profiles`,
      payload: {
        profiles,
        metadata: {
          aiGenerated: true,
          model: Var.ai.openai.model,
          generatedAt: new Date().toISOString(),
          inputAttributes: attributes.length,
          totalPossibleCombinations: attributes.reduce((total, attr) => total * (attributeLevels[attr.value]?.length || 1), 1),
        },
      },
    });
  } catch (error) {
    logger.error('Error generating conjoint analysis profiles', {
      error: error instanceof Error ? error.message : String(error),
      accountId: res.locals?.accountId,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return res.status(500).json({
      success: false,
      message: `${Var.app.emoji.failure} Failed to generate conjoint analysis profiles. Please try again.`,
    });
  }
};
