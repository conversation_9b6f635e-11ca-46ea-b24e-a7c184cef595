import { Var } from '../../../global/var';

/**
 * Validate AI configuration and availability
 * 
 * @returns Object with validation results
 */
export const validateAIConfiguration = () => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if OpenAI API key is configured
  if (!Var.ai.openai.apiKey) {
    errors.push('OpenAI API key is not configured');
  }

  // Check if API key format looks valid
  if (Var.ai.openai.apiKey && !Var.ai.openai.apiKey.startsWith('sk-')) {
    warnings.push('OpenAI API key format may be invalid');
  }

  // Check if model is configured
  if (!Var.ai.openai.model) {
    warnings.push('OpenAI model not specified, using default');
  }

  return {
    isValid: errors.length === 0,
    isConfigured: !!Var.ai.openai.apiKey,
    errors,
    warnings,
    config: {
      model: Var.ai.openai.model,
      hasApiKey: !!Var.ai.openai.apiKey,
    }
  };
};
